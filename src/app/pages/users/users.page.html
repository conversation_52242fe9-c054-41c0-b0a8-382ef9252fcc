<!-- Loading state with shimmer effect -->
<div *ngIf="isLoading" class="shimmer-container">
  <!-- Shimmer for search card -->
  <div class="shimmer-search-card">
    <div class="shimmer-search-input"></div>
    <div class="shimmer-filters">
      <div class="shimmer-filter" *ngFor="let i of [1,2,3]"></div>
    </div>
    <div class="shimmer-button"></div>
  </div>

  <!-- Shimmer for table rows -->
  <div class="shimmer-table">
    <div class="shimmer-header"></div>
    <div class="shimmer-row" *ngFor="let i of [1,2,3,4,5]"></div>
  </div>
</div>

<!-- Search card with filters - show only when not loading AND users exist -->
<div *ngIf="!isLoading && usersData && usersData.length > 0" class="search-card">
  <div class="search-row">
    <div class="search-wrapper">
      <ion-icon name="search-outline"></ion-icon>
      <input type="text" placeholder="Search users..." [(ngModel)]="searchTerm" (input)="filterUsersBasedOnSearch()">
    </div>

    <!-- Agent Name Display -->
    <div class="agent-info-section">
      <div class="agent-name-container">
        <ion-icon name="business-outline" class="agent-icon"></ion-icon>
        <div class="agent-details">
          <span class="agent-name">{{agentName}}</span>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <button class="action-btn image-upload" (change)="uploadFiles(file.files)">
        <label for="file-input">
          <ion-icon name="cloud-upload-outline"></ion-icon>
          Import
        </label>
        <input #file id="file-input" type="file" accept=".csv" multiple />
      </button>
      <button class="action-btn" (click)="showHelpButtonClicked()">
        <ion-icon name="help-circle-outline"></ion-icon>
        Help
      </button>
      <button class="add-btn" (click)="addUpdateModal()">
        <ion-icon name="add-outline"></ion-icon>
        Add User
      </button>
    </div>
  </div>
</div>

<!-- Zero data state when no users available at all and not loading -->
<div *ngIf="!isLoading && (!usersData || usersData.length === 0)" class="empty-state-container">
  <div class="empty-state-content">
    <ion-icon name="people-outline" class="empty-state-icon"></ion-icon>
    <h2>No Users Available</h2>
    <p>There are currently no users to display.</p>
    <p>You can add a new user or import users from a CSV file.</p>
    <div class="empty-state-actions">
      <button class="add-btn" (click)="addUpdateModal()">
        <ion-icon name="add-outline"></ion-icon>
        Add User
      </button>
      <button class="action-btn secondary image-upload" (change)="uploadFiles(file.files)">
        <label for="file-input-empty">
          <ion-icon name="cloud-upload-outline"></ion-icon>
          Import Users
        </label>
        <input #file id="file-input-empty" type="file" accept=".csv" multiple />
      </button>
    </div>
  </div>
</div>

<!-- Regular content when usersData has data -->
<div *ngIf="!isLoading && usersData && usersData.length > 0" class="users-table-wrapper">
  <div class="users-table">
    <div class="table-header">
      <div class="header-cell id-cell">ID</div>
      <div class="header-cell name-cell">Name</div>
      <div class="header-cell email-cell">Email</div>
      <div class="header-cell phone-cell">Phone No</div>
      <div class="header-cell role-cell">Role</div>
      <div class="header-cell actions-cell">Actions</div>
    </div>

    <!-- Show filtered results when available -->
    <div class="table-row" *ngFor="let user of filteredData">
      <div class="table-cell id-cell" data-label="ID">{{user.USER_ID}}</div>
      <div class="table-cell name-cell" data-label="Name">{{user.FIRST_NAME}} {{user.LAST_NAME}}</div>
      <div class="table-cell email-cell" data-label="Email">
        <a [href]="'mailto:' + user.EMAIL" class="email-link">{{user.EMAIL}}</a>
      </div>
      <div class="table-cell phone-cell" data-label="Phone No">{{formatPhoneNumber(user.PHONE)}}</div>
      <div class="table-cell role-cell" data-label="Role">
        <span class="role-badge" [ngClass]="getRoleBadgeClass(user.ROLE_NAME)">{{user.ROLE_NAME}}</span>
      </div>
      <div class="table-cell actions-cell">
        <!-- <ion-button fill="clear" size="small" color="primary" (click)="resetPassword(user.EMAIL)" *ngIf="!isAgentIsInternal" title="Reset Password">
          <ion-icon name="key-outline"></ion-icon>
        </ion-button> -->
        <ion-button fill="clear" size="small" color="primary" (click)="addUpdateModal(user)" title="Edit User">
          <ion-icon name="create-outline"></ion-icon>
        </ion-button>
      </div>
    </div>

    <!-- Show empty state message when filtered list is empty but usersData has items -->
    <div *ngIf="filteredData.length === 0 && usersData.length > 0" class="no-results-message">
      <div class="no-results-content">
        <ion-icon name="filter-outline"></ion-icon>
        <p>No users match the current search criteria</p>
        <button class="action-btn" (click)="closeSearchBar()">
          <ion-icon name="close-circle-outline"></ion-icon>
          Clear Search
        </button>
      </div>
    </div>
  </div>
</div>